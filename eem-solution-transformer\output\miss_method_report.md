# 缺失方法源码信息分析报告

生成时间: 2025-08-29 13:37:51
分析的错误数量: 2

---

## 错误 1: TeamEnergyServiceImpl.getProjectTree()

### 基本信息
- **错误ID**: 1
- **模块**: eem-solution-transformer-core
- **包名**: com.cet.eem.fusion.groupenergy.core.service.impl
- **类名**: TeamEnergyServiceImpl
- **缺失方法**: getProjectTree()
- **文件路径**: src/main/java/com/cet/eem/fusion/groupenergy/core/service/impl/TeamEnergyServiceImpl.java
- **行号**: 466

### 错误描述
无法解析方法 'getProjectTree(Integer)'

### 源码上下文分析
**注意**: 由于source-context-extractor工具调用失败，以下为基于错误信息的初步分析：

- **方法签名**: getProjectTree()
- **完整签名**: getProjectTree(Integer)
- **所在类**: TeamEnergyServiceImpl
- **所在包**: com.cet.eem.fusion.groupenergy.core.service.impl

### 需要进一步分析
- [ ] 查找该方法在新框架中的对应实现
- [ ] 分析方法参数类型和返回值
- [ ] 检查是否有替代方法或API
- [ ] 确认导入语句和依赖配置

---

## 错误 2: EnergyController.queryEnergyData()

### 基本信息
- **错误ID**: 2
- **模块**: eem-solution-transformer-service
- **包名**: com.cet.eem.fusion.groupenergy.service.controller
- **类名**: EnergyController
- **缺失方法**: queryEnergyData()
- **文件路径**: src/main/java/com/cet/eem/fusion/groupenergy/service/controller/EnergyController.java
- **行号**: 123

### 错误描述
无法解析方法 'queryEnergyData(String, Date, Date)'

### 源码上下文分析
**注意**: 由于source-context-extractor工具调用失败，以下为基于错误信息的初步分析：

- **方法签名**: queryEnergyData()
- **完整签名**: queryEnergyData(String, Date, Date)
- **所在类**: EnergyController
- **所在包**: com.cet.eem.fusion.groupenergy.service.controller

### 需要进一步分析
- [ ] 查找该方法在新框架中的对应实现
- [ ] 分析方法参数类型和返回值
- [ ] 检查是否有替代方法或API
- [ ] 确认导入语句和依赖配置

## 总结

本报告分析了 2 个缺失方法错误。
建议使用知识库搜索相关解决方案，或手动检查源码以确定正确的替代方法。
